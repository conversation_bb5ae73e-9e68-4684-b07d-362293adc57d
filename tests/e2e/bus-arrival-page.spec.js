import { test, expect } from '@playwright/test';

test.describe('Bus Arrival Page', () => {
  test('should load bus arrival page for stop 08057', async ({ page }) => {
    await page.goto('/bus-arrival/#08057');
    
    // Check that the page title contains arrival information
    await expect(page).toHaveTitle(/Bus arrival times/);
    
    // Check that the arrivals container is present
    await expect(page.locator('#arrivals')).toBeVisible();
    
    // Wait for the page to load arrival data
    await page.waitForTimeout(5000);
    
    // Check that we're on the correct route by looking at the URL hash
    await expect(page).toHaveURL(/\/bus-arrival\/#08057/);
  });

  test('should display arrival information', async ({ page }) => {
    await page.goto('/bus-arrival/#08057');
    
    // Wait for arrival data to load
    await page.waitForTimeout(5000);
    
    // Check that the arrivals container has content
    const arrivalsContent = page.locator('#arrivals');
    await expect(arrivalsContent).toBeVisible();
    
    // Check for arrival-related content
    const textContent = await arrivalsContent.textContent();
    expect(textContent).toBeTruthy();
    
    // Look for typical arrival page elements (times, service numbers, etc.)
    // The content might include "min", service numbers, or arrival times
    const hasArrivalContent = await page.locator('text=/min|arrival|service|\d+/i').count();
    expect(hasArrivalContent).toBeGreaterThanOrEqual(0); // Could be 0 if no buses are arriving
  });

  test('should handle stop with no arrivals', async ({ page }) => {
    await page.goto('/bus-arrival/#08057');
    
    // Wait for the page to load
    await page.waitForTimeout(5000);
    
    // The page should still load properly even if there are no arrivals
    await expect(page.locator('#arrivals')).toBeVisible();
    
    // Check that the page doesn't crash
    const arrivalsContainer = page.locator('#arrivals');
    await expect(arrivalsContainer).toBeVisible();
  });

  test('should handle invalid stop ID', async ({ page }) => {
    await page.goto('/bus-arrival/#invalid123');
    
    // Wait for the page to load
    await page.waitForTimeout(3000);
    
    // The page should still load without crashing
    await expect(page.locator('#arrivals')).toBeVisible();
    
    // Check that the page handles the invalid stop gracefully
    const pageContent = await page.textContent('body');
    expect(pageContent).toBeTruthy();
  });

  test('should have responsive design', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });
    await page.goto('/bus-arrival/#08057');
    
    // Wait for the page to load
    await page.waitForTimeout(3000);
    
    // Check that the arrivals container is still visible on mobile
    await expect(page.locator('#arrivals')).toBeVisible();
    
    // Test desktop viewport
    await page.setViewportSize({ width: 1200, height: 800 });
    await page.reload();
    await page.waitForTimeout(3000);
    
    // Check that the arrivals container is still visible on desktop
    await expect(page.locator('#arrivals')).toBeVisible();
  });
});
