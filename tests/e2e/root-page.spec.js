import { test, expect } from '@playwright/test';

test.describe('Root Page', () => {
  test('should load the main page successfully', async ({ page }) => {
    await page.goto('/');

    // Check that the page title is correct
    await expect(page).toHaveTitle(/BusRouter SG/);

    // Check that the logo is visible
    await expect(page.locator('#logo')).toBeVisible();

    // Check that the map container is present
    await expect(page.locator('#map')).toBeVisible();

    // Wait for the app to load and render content
    // The app loads data asynchronously, so we need to wait for actual content
    await page.waitForFunction(
      () => {
        const app = document.getElementById('app');
        return app && app.children.length > 0;
      },
      { timeout: 15000 },
    );

    // Check that there's some content loaded in the app
    const appContent = page.locator('#app');
    await expect(appContent).not.toBeEmpty();

    // Check for search functionality (main feature of the app)
    await expect(page.locator('input[type="search"]')).toBeVisible();
  });

  test('should have working navigation elements', async ({ page }) => {
    await page.goto('/');

    // Wait for the app to load
    await page.waitForFunction(
      () => {
        const app = document.getElementById('app');
        return app && app.children.length > 0;
      },
      { timeout: 15000 },
    );

    // Check for search input (main navigation element)
    await expect(page.locator('input[type="search"]')).toBeVisible();

    // Check if there are any clickable elements
    const hasClickableElements = await page
      .locator('button, a, [role="button"], input')
      .count();
    expect(hasClickableElements).toBeGreaterThan(0);
  });
});
