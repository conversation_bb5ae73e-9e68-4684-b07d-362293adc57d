import { test, expect } from '@playwright/test';

test.describe('Service Page', () => {
  test('should load service page for service 133', async ({ page }) => {
    await page.goto('/#/services/133');

    // Check that the page title is correct
    await expect(page).toHaveTitle(/BusRouter SG/);

    // Check that the map container is present
    await expect(page.locator('#map')).toBeVisible();

    // Wait for the app to load and render content
    await page.waitForFunction(
      () => {
        const app = document.getElementById('app');
        return app && app.children.length > 0;
      },
      { timeout: 15000 },
    );

    // Check that we're on the correct route by looking at the URL hash
    await expect(page).toHaveURL(/#\/services\/133/);

    // Check that there's content in the app (service information should be loaded)
    const appContent = page.locator('#app');
    await expect(appContent).not.toBeEmpty();

    // Look for service-related content
    const hasServiceContent = await page
      .locator('text=/133|service|route|bus/i')
      .count();
    expect(hasServiceContent).toBeGreaterThan(0);
  });

  test('should display service route information', async ({ page }) => {
    await page.goto('/#/services/133');

    // Wait for the app to load
    await page.waitForFunction(
      () => {
        const app = document.getElementById('app');
        return app && app.children.length > 0;
      },
      { timeout: 15000 },
    );

    // Check for typical service page elements
    const appContent = page.locator('#app');

    // Check that there's meaningful content loaded
    const textContent = await appContent.textContent();
    expect(textContent).toBeTruthy();
    expect(textContent.length).toBeGreaterThan(10);

    // Service pages typically show route information, stops, etc.
    // Check for common service page indicators
    const hasRouteInfo = await page
      .locator('text=/stop|direction|route/i')
      .count();
    expect(hasRouteInfo).toBeGreaterThan(0);
  });

  test('should handle invalid service ID gracefully', async ({ page }) => {
    await page.goto('/#/services/invalid999');

    // Wait for the app to load
    await page.waitForFunction(
      () => {
        const app = document.getElementById('app');
        return app && app.children.length > 0;
      },
      { timeout: 15000 },
    );

    // The app should still load without crashing
    const appContent = page.locator('#app');
    await expect(appContent).not.toBeEmpty();

    // Check that the page doesn't show an error in the console
    await expect(page.locator('#map')).toBeVisible();
  });

  test('should show service route on map', async ({ page }) => {
    await page.goto('/#/services/133');

    // Wait for the app and map to load
    await page.waitForFunction(
      () => {
        const app = document.getElementById('app');
        return app && app.children.length > 0;
      },
      { timeout: 15000 },
    );

    // Check that the map is visible and loaded
    await expect(page.locator('#map')).toBeVisible();

    // The map should contain some visual elements (canvas, svg, etc.)
    const mapContent = page.locator('#map');
    const hasMapElements = await mapContent
      .locator('canvas, svg, .maplibregl-canvas')
      .count();
    expect(hasMapElements).toBeGreaterThan(0);
  });
});
