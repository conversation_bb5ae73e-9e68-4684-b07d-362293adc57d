import { test, expect } from '@playwright/test';

test.describe('Stop Page', () => {
  test('should load stop page for stop 08057', async ({ page }) => {
    await page.goto('/#/stops/08057');

    // Check that the page title is correct
    await expect(page).toHaveTitle(/BusRouter SG/);

    // Check that the map container is present
    await expect(page.locator('#map')).toBeVisible();

    // Wait for the app to load and render content
    await page.waitForFunction(
      () => {
        const app = document.getElementById('app');
        return app && app.children.length > 0;
      },
      { timeout: 15000 },
    );

    // Check that we're on the correct route by looking at the URL hash
    await expect(page).toHaveURL(/#\/stops\/08057/);

    // Check that there's content in the app (stop information should be loaded)
    const appContent = page.locator('#app');
    await expect(appContent).not.toBeEmpty();

    // Look for stop-related content (this might include stop name, services, etc.)
    // We'll check for common elements that might appear on a stop page
    const hasStopContent = await page
      .locator('text=/08057|stop|bus|service/i')
      .count();
    expect(hasStopContent).toBeGreaterThan(0);
  });

  test('should display stop information', async ({ page }) => {
    await page.goto('/#/stops/08057');

    // Wait for the app to load
    await page.waitForFunction(
      () => {
        const app = document.getElementById('app');
        return app && app.children.length > 0;
      },
      { timeout: 15000 },
    );

    // Check for typical stop page elements
    // This might include stop name, services list, etc.
    const appContent = page.locator('#app');

    // Check that there's meaningful content loaded
    const textContent = await appContent.textContent();
    expect(textContent).toBeTruthy();
    expect(textContent.length).toBeGreaterThan(10);
  });

  test('should handle invalid stop ID gracefully', async ({ page }) => {
    await page.goto('/#/stops/invalid123');

    // Wait for the app to load
    await page.waitForFunction(
      () => {
        const app = document.getElementById('app');
        return app && app.children.length > 0;
      },
      { timeout: 15000 },
    );

    // The app should still load without crashing
    const appContent = page.locator('#app');
    await expect(appContent).not.toBeEmpty();

    // Check that the page doesn't show an error in the console
    // (We'll check this by ensuring the page is still functional)
    await expect(page.locator('#map')).toBeVisible();
  });
});
